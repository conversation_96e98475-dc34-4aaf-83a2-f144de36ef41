import { supabaseAdminClient } from '@/db/supabaseClient';
import { getCRMClient } from '../../getCrmClient';
import { handleCompanyToDealAssociation } from '../../webhook/companyAssociationServices';
import { crmWebhookContactPropertyServices } from '../../webhook/contactPropertyServices';
import { handleContactToCompanyAssociation, handleContactToDealAssociation } from '../../webhook/contactAssociationServices';
import { crmWebhookContactCreationServices } from '@/service/crm/webhook/contactCreationServices';
import { crmWebhookCompanyCreationServices } from '@/service/crm/webhook/companyCreationServices';
import { SalesforceContactEvent, SalesforceAccountEvent, SalesforceAssociationEvent } from './types';

export const handleContactCreation = async (event: SalesforceContactEvent) => {
  const contactId = event.subject.Id;
  const tenantId = event.tenantId;

  await crmWebhookContactCreationServices.handleNewContactCreation({
    contactId,
    tenantId,
  });
};

export const handleContactPropertyChange = async (event: SalesforceContactEvent) => {
  const contactId = event.subject.Id;
  const tenantId = event.tenantId;

  const changedFields = event.changeEventHeader?.changedFields || [];

  if (changedFields.includes('Email') && event.subject.Email !== undefined) {
    await crmWebhookContactPropertyServices.handleEmailPropertyChange({
      email: event.subject.Email,
      tenantId,
      contactId,
    });
  }

  if (changedFields.includes('Title') && event.subject.Title !== undefined) {
    await crmWebhookContactPropertyServices.handleJobTitlePropertyChange({
      jobTitle: event.subject.Title,
      tenantId,
      contactId,
    });
  }

  // Handle Name field changes (FirstName, LastName are flattened from Name object)
  if (changedFields.includes('Name') && (event.subject.FirstName || event.subject.LastName)) {
    console.log(`[Tenant ${tenantId}] Contact ${contactId} name changed: ${event.subject.FirstName} ${event.subject.LastName}`);
    // Note: Currently no specific handler for name changes, but we log it for tracking
  }
};

export const handleCompanyAssociationChange = async (event: SalesforceAssociationEvent) => {
  const tenantId = event.tenantId;

  try {
    console.log(`[Tenant ${tenantId}] Processing Salesforce company association: ${event.fromObjectId} <-> ${event.toObjectId}`);

    const crmClient = await getCRMClient({ supabase: supabaseAdminClient, tenantId });

    if (event.relationshipName === 'Account_Opportunity') {
      await handleCompanyToDealAssociation({
        tenantId,
        companyId: event.fromObjectId,
        dealId: event.toObjectId,
        isRemoved: event.eventType === 'deleted',
        crm: crmClient,
      });
    }

    console.log(`[Tenant ${tenantId}] Successfully processed Salesforce company association`);
  } catch (error) {
    console.error(`[Tenant ${tenantId}] Error processing Salesforce company association:`, error);
  }
};

export const handleContactAssociationChange = async (event: SalesforceAssociationEvent) => {
  const tenantId = event.tenantId;

  try {
    console.log(`[Tenant ${tenantId}] Processing Salesforce contact association: ${event.fromObjectId} <-> ${event.toObjectId}`);

    const crmClient = await getCRMClient({ supabase: supabaseAdminClient, tenantId });

    if (event.relationshipName === 'Contact_Opportunity') {
      await handleContactToDealAssociation({
        tenantId,
        contactId: event.fromObjectId,
        dealId: event.toObjectId,
        isRemoved: event.eventType === 'deleted',
        crm: crmClient,
      });
    }

    if (event.relationshipName === 'Contact_Account') {
      await handleContactToCompanyAssociation({
        tenantId,
        contactId: event.fromObjectId,
        companyId: event.toObjectId,
        isRemoved: event.eventType === 'deleted',
        crm: crmClient,
      });
    }

    console.log(`[Tenant ${tenantId}] Successfully processed Salesforce contact association`);
  } catch (error) {
    console.error(`[Tenant ${tenantId}] Error processing Salesforce contact association:`, error);
  }
};

export const handleCompanyCreation = async (event: SalesforceAccountEvent) => {
  const companyId = event.subject.Id;
  const tenantId = event.tenantId;

  await crmWebhookCompanyCreationServices.handleNewCompanyCreation({
    companyId,
    tenantId,
  });
};
